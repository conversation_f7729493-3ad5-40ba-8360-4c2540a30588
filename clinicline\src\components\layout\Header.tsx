import { useState, useEffect } from "react";
import { Bell, Search, User, Clock, Calendar } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { format, isToday, isTomorrow, addHours, parseISO } from "date-fns";

interface Notification {
  id: number;
  type: 'appointment_reminder' | 'appointment_today' | 'appointment_soon';
  title: string;
  message: string;
  time: string;
  appointment_id?: number;
}

export function Header() {
  const { profile } = useAuth();
  const { t } = useLanguage();
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchNotifications();
    // Set up real-time updates every 5 minutes
    const interval = setInterval(fetchNotifications, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const fetchNotifications = async () => {
    try {
      const today = format(new Date(), 'yyyy-MM-dd');
      const tomorrow = format(addHours(new Date(), 24), 'yyyy-MM-dd');

      // Fetch today's and tomorrow's appointments
      const { data: appointments, error } = await supabase
        .from('appointments')
        .select(`
          id,
          appointment_date,
          appointment_time,
          status,
          patients!inner (
            first_name,
            last_name
          )
        `)
        .in('appointment_date', [today, tomorrow])
        .in('status', ['confirmed', 'completed'])
        .order('appointment_time');

      if (error) {
        console.error('Error fetching notifications:', error);
        return;
      }

      const newNotifications: Notification[] = [];

      appointments?.forEach((appointment) => {
        const appointmentDate = appointment.appointment_date;
        const patientName = `${appointment.patients.first_name} ${appointment.patients.last_name}`;

        if (appointmentDate === today) {
          newNotifications.push({
            id: appointment.id,
            type: 'appointment_today',
            title: 'Appointment Today',
            message: `${patientName} at ${appointment.appointment_time}`,
            time: appointment.appointment_time,
            appointment_id: appointment.id,
          });
        } else if (appointmentDate === tomorrow) {
          newNotifications.push({
            id: appointment.id + 1000, // Offset to avoid ID conflicts
            type: 'appointment_reminder',
            title: 'Appointment Tomorrow',
            message: `${patientName} at ${appointment.appointment_time}`,
            time: appointment.appointment_time,
            appointment_id: appointment.id,
          });
        }
      });

      setNotifications(newNotifications);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case "doctor":
        return "default";
      case "nurse":
        return "secondary";
      default:
        return "outline";
    }
  };

  const handleProfileClick = () => {
    navigate('/profile');
  };



  return (
    <header className="h-16 bg-card border-b border-border flex items-center justify-between px-6 shadow-soft">
      <div className="flex items-center gap-4">
        <SidebarTrigger className="text-muted-foreground hover:text-foreground" />
        
      </div>

      <div className="flex items-center gap-4">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="relative">
              <Bell className="w-5 h-5" />
              {notifications.length > 0 && (
                <Badge
                  variant="destructive"
                  className="absolute -top-1 -right-1 w-5 h-5 rounded-full p-0 flex items-center justify-center text-xs"
                >
                  {notifications.length}
                </Badge>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-80">
            <DropdownMenuLabel className="flex items-center gap-2">
              <Bell className="w-4 h-4" />
              {t('header.notifications')}
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            {loading ? (
              <DropdownMenuItem disabled>
                {t('common.loading')}
              </DropdownMenuItem>
            ) : notifications.length === 0 ? (
              <DropdownMenuItem disabled>
                {t('appointments.noNewNotifications')}
              </DropdownMenuItem>
            ) : (
              notifications.map((notification) => (
                <DropdownMenuItem key={notification.id} className="flex flex-col items-start gap-1 p-3">
                  <div className="flex items-center gap-2 w-full">
                    {notification.type === 'appointment_today' ? (
                      <Calendar className="w-4 h-4 text-success" />
                    ) : (
                      <Clock className="w-4 h-4 text-warning" />
                    )}
                    <span className="font-medium text-sm">{notification.title}</span>
                  </div>
                  <p className="text-sm text-muted-foreground">{notification.message}</p>
                </DropdownMenuItem>
              ))
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        <Button variant="ghost" className="flex items-center gap-3" onClick={handleProfileClick}>
          <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center">
            <User className="w-4 h-4 text-white" />
          </div>
          <div className="text-left">
            <p className="text-sm font-medium">
              {profile ? `${profile.first_name} ${profile.last_name}` : t('ui.user')}
            </p>
            <Badge variant={getRoleBadgeVariant(profile?.role || 'nurse')} className="text-xs">
              {profile?.role ? profile.role.charAt(0).toUpperCase() + profile.role.slice(1) : t('ui.user')}
            </Badge>
          </div>
        </Button>
      </div>
    </header>
  );
}