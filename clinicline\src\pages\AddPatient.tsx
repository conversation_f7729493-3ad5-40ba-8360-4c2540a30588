import { ArrowLeft } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AddPatientForm } from "@/components/forms/AddPatientForm";
import { useLanguage } from "@/contexts/LanguageContext";

export default function AddPatient() {
  const navigate = useNavigate();
  const { t } = useLanguage();

  const handleSuccess = () => {
    // Navigate back to patients page after successful submission
    navigate("/patients");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link to="/patients">
          <Button variant="outline" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            {t('patient.backToPatients')}
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-foreground">{t('patient.addNew')}</h1>
          <p className="text-muted-foreground">
            {t('patient.information')}
          </p>
        </div>
      </div>

      <Card className="shadow-soft max-w-4xl">
        <CardHeader>
          <CardTitle>{t('patient.information')}</CardTitle>
        </CardHeader>
        <CardContent>
          <AddPatientForm onSuccess={handleSuccess} />
        </CardContent>
      </Card>
    </div>
  );
}