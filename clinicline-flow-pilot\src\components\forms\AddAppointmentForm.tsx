import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { CalendarIcon, Clock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useToast } from "@/hooks/use-toast";
import { useLanguage } from "@/contexts/LanguageContext";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { cn } from "@/lib/utils";

const appointmentSchema = z.object({
  firstName: z.string().min(1, "Veuillez saisir le prénom"),
  lastName: z.string().min(1, "Veuillez saisir le nom de famille"),
  appointmentDate: z.date({
    required_error: "Veuillez sélectionner une date",
  }),
  appointmentTime: z.string().min(1, "Veuillez sélectionner une heure"),
  status: z.enum(["confirmed", "completed", "cancelled"]),
  notes: z.string().optional(),
});

type AppointmentFormData = z.infer<typeof appointmentSchema>;

interface AddAppointmentFormProps {
  onSuccess?: () => void;
  initialData?: {
    appointmentDate?: Date;
    appointmentTime?: string;
    firstName?: string;
    lastName?: string;
    status?: "confirmed" | "completed" | "cancelled";
    notes?: string;
  };
  isEditing?: boolean;
  appointmentId?: number;
}

const timeSlots = [
  "08:00", "08:30", "09:00", "09:30", "10:00", "10:30",
  "11:00", "11:30", "12:00", "12:30", "13:00", "13:30", 
  "14:00", "14:30", "15:00", "15:30", "16:00", "16:30", 
  "17:00", "17:30"
];

export function AddAppointmentForm({ onSuccess, initialData, isEditing = false, appointmentId }: AddAppointmentFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { t } = useLanguage();
  const { profile, user } = useAuth();
  const { toast } = useToast();

  const form = useForm<AppointmentFormData>({
    resolver: zodResolver(appointmentSchema),
    defaultValues: {
      firstName: initialData?.firstName || "",
      lastName: initialData?.lastName || "",
      appointmentDate: initialData?.appointmentDate,
      appointmentTime: initialData?.appointmentTime || "",
      status: initialData?.status || "confirmed",
      notes: initialData?.notes || "",
    },
  });

  const watchedDate = form.watch("appointmentDate");
  const watchedTime = form.watch("appointmentTime");

  // Available time slots
  const timeSlots = [
    "08:00", "08:30", "09:00", "09:30", "10:00", "10:30", "11:00", "11:30",
    "12:00", "12:30", "13:00", "13:30", "14:00", "14:30", "15:00", "15:30",
    "16:00", "16:30", "17:00", "17:30"
  ];

  const checkDoubleBooking = async (date: Date, time: string) => {
    try {
      const { data, error } = await supabase
        .from('appointments')
        .select('id')
        .eq('appointment_date', format(date, 'yyyy-MM-dd'))
        .eq('appointment_time', time)
        .neq('status', 'cancelled');

      if (error) {
        console.error("Error checking double booking:", error);
        return false;
      }

      return data && data.length > 0;
    } catch (error) {
      console.error("Error checking double booking:", error);
      return false;
    }
  };

  const onSubmit = async (data: AppointmentFormData) => {
    setIsSubmitting(true);

    try {
      // Check authentication first
      const { data: { user: currentUser }, error: authError } = await supabase.auth.getUser();

      if (!currentUser) {
        toast({
          title: "Authentication Error",
          description: "You must be logged in to create appointments",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      // Validate required fields
      if (!data.firstName || !data.lastName || !data.appointmentDate || !data.appointmentTime) {
        toast({
          title: "Error",
          description: "Please fill in all required fields",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }



      // First, create or find the patient
      let patientId: number;

      // Check if patient already exists
      const { data: existingPatients, error: searchError } = await supabase
        .from('patients')
        .select('id')
        .eq('first_name', data.firstName)
        .eq('last_name', data.lastName)
        .limit(1);

      if (searchError) {
        console.error("Error searching for patient:", searchError);
        toast({
          title: "Error",
          description: `Failed to search for patient: ${searchError.message}`,
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      if (existingPatients && existingPatients.length > 0) {
        // Patient exists, use their ID
        patientId = existingPatients[0].id;

      } else {
        // Patient doesn't exist, create new one
        const { data: newPatient, error: patientError } = await supabase
          .from('patients')
          .insert({
            first_name: data.firstName,
            last_name: data.lastName,
          })
          .select('id')
          .single();

        if (patientError) {
          console.error("Error creating patient:", patientError);
          toast({
            title: "Error",
            description: `Failed to create patient: ${patientError.message}`,
            variant: "destructive",
          });
          setIsSubmitting(false);
          return;
        }

        patientId = newPatient.id;

      }

      // Check for double booking
      const isDoubleBooked = await checkDoubleBooking(data.appointmentDate, data.appointmentTime);

      if (isDoubleBooked) {
        toast({
          title: "Créneau Indisponible",
          description: `Le créneau sélectionné (${data.appointmentTime} le ${format(data.appointmentDate, 'dd MMM yyyy', { locale: fr })}) est déjà réservé. Veuillez choisir un autre horaire.`,
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }



      // Prepare appointment data
      const appointmentData = {
        patient_id: patientId,
        appointment_date: format(data.appointmentDate, 'yyyy-MM-dd'),
        appointment_time: data.appointmentTime,
        status: data.status,
        notes: data.notes || null,
        created_by: currentUser.id,
      };





      // Insert or update appointment in database
      let result;
      if (isEditing && appointmentId) {
        // Update existing appointment
        result = await supabase
          .from('appointments')
          .update(appointmentData)
          .eq('id', appointmentId)
          .select();
      } else {
        // Insert new appointment
        result = await supabase
          .from('appointments')
          .insert(appointmentData)
          .select();
      }

      const { data: appointmentResult, error } = result;

      if (error) {
        console.error(`Error ${isEditing ? 'updating' : 'adding'} appointment:`, error);
        console.error("Error details:", error.details);
        console.error("Error hint:", error.hint);
        toast({
          title: "Error",
          description: `Failed to ${isEditing ? 'update' : 'schedule'} appointment: ${error.message}`,
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }



      // Show success toast
      toast({
        title: "Success",
        description: `Appointment ${isEditing ? 'updated' : 'scheduled'} for ${data.firstName} ${data.lastName} on ${format(data.appointmentDate, 'MMM dd, yyyy')} at ${data.appointmentTime}`,
      });
      
      // Reset form
      form.reset();
      
      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error adding appointment:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred while scheduling the appointment",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Form is available to all users

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="firstName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('form.firstName')} *</FormLabel>
                <FormControl>
                  <Input placeholder={t('validation.enterFirstName')} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="lastName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('form.lastName')} *</FormLabel>
                <FormControl>
                  <Input placeholder={t('validation.enterLastName')} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="appointmentDate"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>{t('form.appointmentDate')} *</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP", { locale: fr })
                        ) : (
                          <span>{t('date.selectDate')}</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) => date < new Date()}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="appointmentTime"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('form.appointmentTime')} *</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder={t('time.selectTime')}>
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4" />
                          {field.value || t('time.selectTime')}
                        </div>
                      </SelectValue>
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {timeSlots.map((time) => (
                      <SelectItem key={time} value={time}>
                        {time}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('form.status')}</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('form.status')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="confirmed">{t('status.confirmed')}</SelectItem>
                  <SelectItem value="completed">{t('status.completed')}</SelectItem>
                  <SelectItem value="cancelled">{t('status.cancelled')}</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {watchedDate && watchedTime && (
          <div className="p-3 rounded-md bg-muted/50 border">
            <p className="text-sm text-muted-foreground">
              📅 Rendez-vous programmé pour le {format(watchedDate, "dd MMM yyyy", { locale: fr })} à {watchedTime}
            </p>
          </div>
        )}

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('form.notes')} ({t('form.optional')})</FormLabel>
              <FormControl>
                <Textarea
                  placeholder={t('form.notes')}
                  rows={4}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex gap-4 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => form.reset()}
            disabled={isSubmitting}
          >
            {t('common.reset')}
          </Button>

          <Button
            type="submit"
            disabled={isSubmitting}
            className="flex-1"
          >
            {isSubmitting
              ? (isEditing ? t('common.loading') : t('common.loading'))
              : (isEditing ? t('appointment.schedule') : t('appointment.schedule'))
            }
          </Button>
        </div>
      </form>
    </Form>
  );
}