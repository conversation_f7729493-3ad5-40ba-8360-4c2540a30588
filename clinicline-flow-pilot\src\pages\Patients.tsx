import { useState, useEffect } from "react";
import { Plus, Edit, Trash2, Eye, Phone, Mail, Calendar, User, Search } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { supabase } from "@/integrations/supabase/client";
import { useLanguage } from "@/contexts/LanguageContext";
import { useToast } from "@/hooks/use-toast";
import { format, differenceInYears } from "date-fns";
import { fr } from "date-fns/locale";
import { AddPatientForm } from "@/components/forms/AddPatientForm";

interface Patient {
  id: number;
  first_name: string;
  last_name: string;
  birth_date?: string;
  address?: string;
  phone_number?: string;
  medical_history?: string;
  allergies?: string;
  medicament?: string;
  profession?: string;
  code_cnss?: string;
  code_cnrps?: string;
  created_at: string;
}

export default function Patients() {
  const { t } = useLanguage();
  const [patients, setPatients] = useState<Patient[]>([]);
  const [filteredPatients, setFilteredPatients] = useState<Patient[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [patientToDelete, setPatientToDelete] = useState<Patient | null>(null);
  const [newPatient, setNewPatient] = useState({
    first_name: '',
    last_name: '',
    birth_date: '',
    phone_number: '',
    address: '',
    medical_history: '',
    medicament: '',
    profession: '',
    code_cnss: '',
    code_cnrps: ''
  });
  const { toast } = useToast();

  useEffect(() => {
    fetchPatients();
  }, []);

  // Search functionality
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredPatients(patients);
    } else {
      const filtered = patients.filter(patient => {
        const fullName = `${patient.first_name} ${patient.last_name}`.toLowerCase();
        const query = searchQuery.toLowerCase();
        return (
          fullName.includes(query) ||
          patient.phone_number?.toLowerCase().includes(query) ||
          patient.address?.toLowerCase().includes(query) ||
          patient.medical_history?.toLowerCase().includes(query) ||
          patient.medicament?.toLowerCase().includes(query) ||
          patient.profession?.toLowerCase().includes(query) ||
          patient.code_cnss?.toLowerCase().includes(query) ||
          patient.code_cnrps?.toLowerCase().includes(query)
        );
      });
      setFilteredPatients(filtered);
    }
  }, [searchQuery, patients]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const fetchPatients = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('patients')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching patients:', error);
        toast({
          title: "Error",
          description: "Failed to fetch patients",
          variant: "destructive",
        });
      } else {
        setPatients(data || []);
        setFilteredPatients(data || []);
      }
    } catch (error) {
      console.error('Error fetching patients:', error);
    } finally {
      setLoading(false);
    }
  };

  const deletePatient = async (patientId: number) => {
    try {
      const { error } = await supabase
        .from('patients')
        .delete()
        .eq('id', patientId);

      if (error) {
        console.error('Error deleting patient:', error);
        toast({
          title: "Error",
          description: "Failed to delete patient",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Success",
          description: "Patient deleted successfully",
        });
        fetchPatients(); // Refresh the list
        setDeleteDialogOpen(false);
        setPatientToDelete(null);
      }
    } catch (error) {
      console.error('Error deleting patient:', error);
    }
  };

  const handleAddPatient = async () => {
    try {
      if (!newPatient.first_name || !newPatient.last_name) {
        toast({
          title: t('common.error'),
          description: t('message.required'),
          variant: "destructive",
        });
        return;
      }

      const { error } = await supabase
        .from('patients')
        .insert([newPatient]);

      if (error) {
        console.error('Error adding patient:', error);
        toast({
          title: t('common.error'),
          description: "Failed to add patient",
          variant: "destructive",
        });
      } else {
        toast({
          title: t('common.success'),
          description: t('message.patientAdded'),
        });
        fetchPatients(); // Refresh the list
        setAddDialogOpen(false);
        setNewPatient({
          first_name: '',
          last_name: '',
          birth_date: '',
          phone_number: '',
          address: '',
          medical_history: '',
          medicament: '',
          profession: '',
          code_cnss: '',
          code_cnrps: ''
        });
      }
    } catch (error) {
      console.error('Error adding patient:', error);
    }
  };

  const calculateAge = (birthDate?: string) => {
    if (!birthDate) return 'N/A';
    return differenceInYears(new Date(), new Date(birthDate));
  };



  return (
    <div className="space-y-6 relative">
      {/* Enhanced Header */}
      <div className="bg-gradient-to-r from-violet-600 via-purple-600 to-fuchsia-600 rounded-2xl p-6 text-white shadow-xl">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold mb-2">Gestion des Patients</h1>
            <p className="text-violet-100 text-lg">
              Gérez vos dossiers et informations patients - {format(new Date(), 'EEEE dd MMMM yyyy', { locale: fr })}
            </p>
            <div className="flex items-center gap-4 mt-3">
              <div className="flex items-center gap-2 bg-white/20 rounded-full px-3 py-1">
                <div className="w-2 h-2 rounded-full bg-green-400"></div>
                <span className="text-sm">{t('patients.totalPatients')}: {patients.length}</span>
              </div>
              <div className="flex items-center gap-2 bg-white/20 rounded-full px-3 py-1">
                <div className="w-2 h-2 rounded-full bg-blue-400"></div>
                <span className="text-sm">With Phone: {patients.filter(p => p.phone_number).length}</span>
              </div>
              {searchQuery && (
                <div className="flex items-center gap-2 bg-white/20 rounded-full px-3 py-1">
                  <div className="w-2 h-2 rounded-full bg-yellow-400"></div>
                  <span className="text-sm">Found: {filteredPatients.length}</span>
                </div>
              )}
            </div>
          </div>
          <div className="flex flex-col gap-3">
            <Button
              className="bg-white text-violet-600 hover:bg-violet-50 shadow-lg font-semibold px-6 py-3 text-lg"
              onClick={() => setAddDialogOpen(true)}
            >
              <Plus className="w-5 h-5 mr-2" />
              {t('patients.addNewPatient')}
            </Button>
            <div className="text-center text-violet-100 text-sm">
              {t('patients.addNewPatientDesc')}
            </div>
          </div>
        </div>
      </div>





      {/* Search Bar */}
      <Card className="shadow-soft mb-6">
        <CardContent className="p-4">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder={t('patients.searchPlaceholder')}
              value={searchQuery}
              onChange={handleSearchChange}
              className="pl-10 bg-muted/50 border-0 focus:bg-background"
            />
          </div>
          {searchQuery && (
            <p className="text-sm text-muted-foreground mt-2">
              {filteredPatients.length === 0
                ? t('patients.noResults')
                : `${filteredPatients.length} ${filteredPatients.length === 1 ? 'patient' : 'patients'} found`
              }
            </p>
          )}
        </CardContent>
      </Card>

      {/* Enhanced Patients Table */}
      <Card className="shadow-lg border-0">
        <CardHeader className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-t-lg">
          <CardTitle className="flex items-center gap-2 text-indigo-800 text-xl">
            <User className="w-6 h-6" />
            Patient Records ({filteredPatients.length})
          </CardTitle>
          <p className="text-sm text-indigo-600 mt-1">Complete patient database with contact information</p>
        </CardHeader>
        <CardContent className="p-0">
          <div className="max-h-96 overflow-y-auto">
            <Table>
              <TableHeader className="bg-gradient-to-r from-gray-50 to-gray-100">
                <TableRow className="border-b-2 border-gray-200">
                  <TableHead className="font-bold text-gray-700 py-4">
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4" />
                      Patient
                    </div>
                  </TableHead>
                  <TableHead className="font-bold text-gray-700">Age</TableHead>
                  <TableHead className="font-bold text-gray-700">
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4" />
                      Contact
                    </div>
                  </TableHead>
                  <TableHead className="font-bold text-gray-700">Address</TableHead>
                  <TableHead className="font-bold text-gray-700">Medical History</TableHead>
                  <TableHead className="font-bold text-gray-700 text-center">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-12">
                      <div className="flex flex-col items-center gap-3">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                        <p className="text-muted-foreground">Loading patients...</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredPatients.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-12">
                      <div className="flex flex-col items-center gap-4">
                        <User className="w-16 h-16 text-gray-300" />
                        <div>
                          {patients.length === 0 ? (
                            <>
                              <h3 className="text-lg font-semibold text-gray-600 mb-2">{t('patients.noPatients')}</h3>
                              <p className="text-gray-400 mb-4">Start by adding your first patient</p>
                              <Button
                                onClick={() => setAddDialogOpen(true)}
                                className="bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700"
                              >
                                <Plus className="w-4 h-4 mr-2" />
                                {t('patients.addNewPatient')}
                              </Button>
                            </>
                          ) : (
                            <>
                              <h3 className="text-lg font-semibold text-gray-600 mb-2">{t('patients.noResults')}</h3>
                              <p className="text-gray-400">Try adjusting your search terms</p>
                            </>
                          )}
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
              ) : (
                filteredPatients.map((patient) => (
                  <TableRow key={patient.id} className="hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 transition-all duration-200 border-b border-gray-100">
                    <TableCell className="py-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold">
                          {patient.first_name.charAt(0)}{patient.last_name.charAt(0)}
                        </div>
                        <div>
                          <div className="font-bold text-gray-800 text-lg">
                            {patient.first_name} {patient.last_name}
                          </div>
                          <div className="text-sm text-purple-600 font-medium">
                            Patient ID: #{patient.id.toString().padStart(4, '0')}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{calculateAge(patient.birth_date)}</TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {patient.phone_number && (
                          <div className="flex items-center gap-1 text-sm">
                            <Phone className="w-3 h-3" />
                            {patient.phone_number}
                          </div>
                        )}
                        <div className="text-sm text-muted-foreground">
                          <Calendar className="w-3 h-3 inline mr-1" />
                          {patient.birth_date ? format(new Date(patient.birth_date), 'dd MMM yyyy', { locale: fr }) : 'N/A'}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {patient.address ? (
                        <span className="text-sm">{patient.address}</span>
                      ) : (
                        <span className="text-muted-foreground text-sm">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {patient.medical_history ? (
                        <span className="text-sm">{patient.medical_history.substring(0, 50)}...</span>
                      ) : (
                        <span className="text-muted-foreground text-sm">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2 justify-center">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setSelectedPatient(patient);
                            setViewDialogOpen(true);
                          }}
                          className="text-blue-600 border-blue-200 hover:bg-blue-50"
                        >
                          <Eye className="w-3 h-3 mr-1" />
                          {t('common.view')}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setSelectedPatient(patient);
                            setEditDialogOpen(true);
                          }}
                          className="text-green-600 border-green-200 hover:bg-green-50"
                        >
                          <Edit className="w-3 h-3 mr-1" />
                          {t('common.edit')}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setPatientToDelete(patient);
                            setDeleteDialogOpen(true);
                          }}
                          className="text-red-600 border-red-200 hover:bg-red-50"
                        >
                          <Trash2 className="w-3 h-3 mr-1" />
                          {t('common.delete')}
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* View Patient Dialog */}
      <Dialog open={viewDialogOpen} onOpenChange={setViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{t('patients.patientDetails')}</DialogTitle>
            <DialogDescription>
              View detailed information about the patient
            </DialogDescription>
          </DialogHeader>
          {selectedPatient && (
            <div className="grid gap-6 py-4">
              {/* Personal Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-primary border-b pb-2">Personal Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">{t('form.firstName')}</label>
                    <p className="text-lg font-semibold">{selectedPatient.first_name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">{t('form.lastName')}</label>
                    <p className="text-lg font-semibold">{selectedPatient.last_name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">{t('form.birthDate')}</label>
                    <p>{selectedPatient.birth_date ? format(new Date(selectedPatient.birth_date), 'dd MMMM yyyy', { locale: fr }) : 'Non fourni'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">{t('ui.years')}</label>
                    <p>{calculateAge(selectedPatient.birth_date)}</p>
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-primary border-b pb-2">{t('patients.contactInfo')}</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">{t('form.phone')}</label>
                    <p>{selectedPatient.phone_number || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">{t('form.address')}</label>
                    <p>{selectedPatient.address || 'Not provided'}</p>
                  </div>
                </div>
              </div>

              {/* Professional Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-primary border-b pb-2">Professional & Administrative</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">{t('form.profession')}</label>
                    <p>{selectedPatient.profession || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">{t('form.medicament')}</label>
                    <p>{selectedPatient.medicament || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">{t('form.codeCNSS')}</label>
                    <p>{selectedPatient.code_cnss || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">{t('form.codeCNRPS')}</label>
                    <p>{selectedPatient.code_cnrps || 'Not provided'}</p>
                  </div>
                </div>
              </div>

              {/* Medical Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-primary border-b pb-2">{t('patients.medicalInfo')}</h3>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">{t('form.medicalHistory')}</label>
                    <div className="mt-2 p-3 bg-muted/50 rounded-md">
                      <p className="whitespace-pre-wrap">{selectedPatient.medical_history || 'Aucun antécédent médical enregistré'}</p>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">{t('form.allergies')}</label>
                    <div className="mt-2 p-3 bg-muted/50 rounded-md">
                      <p className="whitespace-pre-wrap">{selectedPatient.allergies || 'Aucune allergie signalée'}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Patient Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Patient</DialogTitle>
            <DialogDescription>
              Update patient information
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedPatient && (
              <AddPatientForm
                onSuccess={() => {
                  setEditDialogOpen(false);
                  fetchPatients();
                }}
                initialData={selectedPatient}
                isEditing={true}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the patient
              {patientToDelete && ` "${patientToDelete.first_name} ${patientToDelete.last_name}"`}
              and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => patientToDelete && deletePatient(patientToDelete.id)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Patient
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Add Patient Dialog */}
      <Dialog open={addDialogOpen} onOpenChange={setAddDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{t('patients.addNewPatient')}</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">{t('form.firstName')} *</Label>
                <Input
                  id="firstName"
                  value={newPatient.first_name}
                  onChange={(e) => setNewPatient({...newPatient, first_name: e.target.value})}
                  placeholder={t('validation.enterFirstName')}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">{t('form.lastName')} *</Label>
                <Input
                  id="lastName"
                  value={newPatient.last_name}
                  onChange={(e) => setNewPatient({...newPatient, last_name: e.target.value})}
                  placeholder={t('validation.enterLastName')}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="birthDate">{t('form.birthDate')}</Label>
                <Input
                  id="birthDate"
                  type="date"
                  value={newPatient.birth_date}
                  onChange={(e) => setNewPatient({...newPatient, birth_date: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phoneNumber">{t('form.phone')}</Label>
                <Input
                  id="phoneNumber"
                  value={newPatient.phone_number}
                  onChange={(e) => setNewPatient({...newPatient, phone_number: e.target.value})}
                  placeholder={t('form.phone')}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="address">{t('form.address')}</Label>
              <Input
                id="address"
                value={newPatient.address}
                onChange={(e) => setNewPatient({...newPatient, address: e.target.value})}
                placeholder={t('form.address')}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="profession">{t('form.profession')}</Label>
              <Input
                id="profession"
                value={newPatient.profession}
                onChange={(e) => setNewPatient({...newPatient, profession: e.target.value})}
                placeholder={t('form.profession')}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="medicament">{t('form.medicament')}</Label>
              <Textarea
                id="medicament"
                value={newPatient.medicament}
                onChange={(e) => setNewPatient({...newPatient, medicament: e.target.value})}
                placeholder={t('form.medicament')}
                rows={3}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="codeCNSS">{t('form.codeCNSS')}</Label>
                <Input
                  id="codeCNSS"
                  value={newPatient.code_cnss}
                  onChange={(e) => setNewPatient({...newPatient, code_cnss: e.target.value})}
                  placeholder={t('form.codeCNSS')}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="codeCNRPS">{t('form.codeCNRPS')}</Label>
                <Input
                  id="codeCNRPS"
                  value={newPatient.code_cnrps}
                  onChange={(e) => setNewPatient({...newPatient, code_cnrps: e.target.value})}
                  placeholder={t('form.codeCNRPS')}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="medicalHistory">{t('form.medicalHistory')}</Label>
              <Textarea
                id="medicalHistory"
                value={newPatient.medical_history}
                onChange={(e) => setNewPatient({...newPatient, medical_history: e.target.value})}
                placeholder={t('form.medicalHistory')}
                rows={4}
              />
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setAddDialogOpen(false)}>
              {t('common.cancel')}
            </Button>
            <Button onClick={handleAddPatient}>
              {t('patient.add')}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

    </div>
  );
}