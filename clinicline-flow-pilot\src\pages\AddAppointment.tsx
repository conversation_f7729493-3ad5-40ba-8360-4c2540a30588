import { ArrowLef<PERSON> } from "lucide-react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AddAppointmentForm } from "@/components/forms/AddAppointmentForm";
import { useLanguage } from "@/contexts/LanguageContext";

export default function AddAppointment() {
  const navigate = useNavigate();
  const { t } = useLanguage();

  const handleSuccess = () => {
    // Navigate back to appointments page after successful submission
    navigate("/appointments");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link to="/appointments">
          <Button variant="outline" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            {t('appointment.backToAppointments')}
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-foreground">{t('appointment.addNew')}</h1>
          <p className="text-muted-foreground">
            {t('appointment.details')}
          </p>
        </div>
      </div>

      <Card className="shadow-soft max-w-4xl">
        <CardHeader>
          <CardTitle>{t('appointment.details')}</CardTitle>
        </CardHeader>
        <CardContent>
          <AddAppointmentForm onSuccess={handleSuccess} />
        </CardContent>
      </Card>
    </div>
  );
}