@tailwind base;
@tailwind components;
@tailwind utilities;

/* ClinicLine Design System - Professional Medical Interface */

@layer base {
  :root {
    --background: 0 0% 98%;
    --foreground: 210 20% 15%;

    --card: 0 0% 100%;
    --card-foreground: 210 20% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 20% 15%;

    --primary: 211 100% 43%;
    --primary-foreground: 0 0% 100%;

    --secondary: 146 60% 92%;
    --secondary-foreground: 146 80% 25%;

    --muted: 210 30% 95%;
    --muted-foreground: 210 15% 50%;

    --accent: 176 60% 88%;
    --accent-foreground: 176 80% 25%;

    --success: 146 80% 35%;
    --success-foreground: 0 0% 100%;

    --warning: 35 90% 60%;
    --warning-foreground: 35 90% 15%;

    --destructive: 0 75% 55%;
    --destructive-foreground: 0 0% 100%;

    --border: 210 20% 88%;
    --input: 210 20% 92%;
    --ring: 211 100% 43%;

    /* Medical gradient themes */
    --gradient-primary: linear-gradient(135deg, hsl(211 100% 43%), hsl(211 90% 55%));
    --gradient-secondary: linear-gradient(135deg, hsl(146 60% 92%), hsl(176 60% 88%));
    --gradient-accent: linear-gradient(180deg, hsl(var(--background)), hsl(210 30% 95%));

    /* Professional shadows */
    --shadow-soft: 0 2px 8px hsl(210 20% 15% / 0.08);
    --shadow-medium: 0 4px 16px hsl(210 20% 15% / 0.12);
    --shadow-strong: 0 8px 32px hsl(210 20% 15% / 0.16);

    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s ease-out;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}