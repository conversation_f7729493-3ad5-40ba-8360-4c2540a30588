-- Add status column to appointments table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointments' AND column_name = 'status') THEN
        ALTER TABLE appointments ADD COLUMN status VARCHAR(20) DEFAULT 'reserved';
    END IF;
END $$;

-- Update any existing appointments without status to have 'reserved' status
UPDATE appointments SET status = 'reserved' WHERE status IS NULL;

-- Add check constraint for valid status values
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints 
                   WHERE constraint_name = 'appointments_status_check') THEN
        ALTER TABLE appointments ADD CONSTRAINT appointments_status_check 
        CHECK (status IN ('reserved', 'done', 'cancelled'));
    END IF;
END $$;
