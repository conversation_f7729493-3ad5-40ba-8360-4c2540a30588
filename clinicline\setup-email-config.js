/**
 * ClinicLine - Supabase Email Configuration Helper
 * 
 * This script provides the configuration values you need to set up
 * email authentication in your Supabase dashboard.
 */

console.log('🔧 ClinicLine - Supabase Email Configuration');
console.log('='.repeat(50));

// Get current environment
const isDevelopment = process.env.NODE_ENV !== 'production';
const currentURL = isDevelopment ? 'http://localhost:8080' : 'https://yourdomain.com';

console.log('\n📍 SITE URL CONFIGURATION:');
console.log('-'.repeat(30));
console.log(`Current Environment: ${isDevelopment ? 'Development' : 'Production'}`);
console.log(`Site URL to set: ${currentURL}`);

console.log('\n🔗 REDIRECT URLS TO ADD:');
console.log('-'.repeat(30));
console.log('Add these URLs to Authentication → Settings → Redirect URLs:');
console.log('• http://localhost:8080/reset-password');
console.log('• https://yourdomain.com/reset-password');
console.log('• http://localhost:8080/auth');
console.log('• https://yourdomain.com/auth');

console.log('\n📧 EMAIL TEMPLATE CONFIGURATION:');
console.log('-'.repeat(30));
console.log('Go to Authentication → Settings → Email Templates → Reset Password');
console.log('Subject: Réinitialisation de votre mot de passe ClinicLine');

const emailTemplate = `
<h2>Réinitialisation de votre mot de passe ClinicLine</h2>

<p>Bonjour,</p>

<p>Vous avez demandé la réinitialisation de votre mot de passe pour votre compte ClinicLine.</p>

<p>Cliquez sur le lien ci-dessous pour créer un nouveau mot de passe :</p>

<p><a href="{{ .SiteURL }}/reset-password?access_token={{ .TokenHash }}&refresh_token={{ .RefreshTokenHash }}&type=recovery" 
   style="background-color: #6366f1; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
   Réinitialiser mon mot de passe
</a></p>

<p>Ce lien expirera dans 1 heure pour des raisons de sécurité.</p>

<p>Si vous n'avez pas demandé cette réinitialisation, vous pouvez ignorer cet email en toute sécurité.</p>

<p>Cordialement,<br>
L'équipe ClinicLine</p>

<hr>
<p style="font-size: 12px; color: #666;">
Si le lien ne fonctionne pas, copiez et collez cette URL dans votre navigateur :<br>
{{ .SiteURL }}/reset-password?access_token={{ .TokenHash }}&refresh_token={{ .RefreshTokenHash }}&type=recovery
</p>
`;

console.log('\nEmail Template HTML:');
console.log(emailTemplate);

console.log('\n⚙️ RECOMMENDED SETTINGS:');
console.log('-'.repeat(30));
console.log('Authentication → Settings → Security:');
console.log('• JWT expiry: 3600 seconds (1 hour)');
console.log('• Refresh token rotation: Enabled');
console.log('• Double confirm email changes: Enabled');

console.log('\nAuthentication → Settings → Rate Limits:');
console.log('• Email sending rate: 30 emails per hour per IP');
console.log('• Password reset rate: 5 attempts per hour per email');

console.log('\n🧪 TESTING INSTRUCTIONS:');
console.log('-'.repeat(30));
console.log('1. Start your app: npm run dev');
console.log('2. Go to: http://localhost:8080/auth');
console.log('3. Click: "Mot de passe oublié ?"');
console.log('4. Enter a valid email address');
console.log('5. Check email inbox for reset email');
console.log('6. Click the reset link and test password reset');

console.log('\n🚀 PRODUCTION CHECKLIST:');
console.log('-'.repeat(30));
console.log('□ Update Site URL to production domain');
console.log('□ Add production redirect URLs');
console.log('□ Configure custom SMTP (recommended)');
console.log('□ Test email delivery from production');
console.log('□ Verify SSL certificates');
console.log('□ Monitor email delivery rates');

console.log('\n✅ Configuration complete! Your reset password feature is ready.');
console.log('='.repeat(50));

// Export configuration for programmatic use
module.exports = {
  siteURL: currentURL,
  redirectURLs: [
    'http://localhost:8080/reset-password',
    'https://yourdomain.com/reset-password',
    'http://localhost:8080/auth',
    'https://yourdomain.com/auth'
  ],
  emailTemplate,
  emailSubject: 'Réinitialisation de votre mot de passe ClinicLine',
  settings: {
    jwtExpiry: 3600,
    refreshTokenRotation: true,
    doubleConfirmEmailChanges: true,
    emailRateLimit: 30,
    passwordResetRateLimit: 5
  }
};
