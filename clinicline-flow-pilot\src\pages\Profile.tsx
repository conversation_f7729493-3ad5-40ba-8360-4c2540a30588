import { ArrowL<PERSON>t, User, Mail, UserCheck } from "lucide-react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
export default function Profile() {
  const { user, profile } = useAuth();
  const { t } = useLanguage();



  if (!user || !profile) {
    return null;
  }

  return (
    <div className="space-y-6 relative">
      {/* Enhanced Header */}
      <div className="bg-gradient-to-r from-rose-600 via-pink-600 to-purple-600 rounded-2xl p-6 text-white shadow-xl">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link to="/">
              <Button variant="outline" size="sm" className="bg-white/20 border-white/30 text-white hover:bg-white/30">
                <ArrowLeft className="w-4 h-4 mr-2" />
                {t('nav.dashboard')}
              </Button>
            </Link>
            <div>
              <h1 className="text-4xl font-bold mb-2">{t('profile.title')}</h1>
              <p className="text-rose-100 text-lg">
                {t('profile.accountSettings')}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
              <User className="w-8 h-8 text-white" />
            </div>
          </div>
        </div>
      </div>

      <div className="grid gap-6 max-w-4xl mx-auto">
        {/* Enhanced Profile Information */}
        <Card className="shadow-lg border-0">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg pb-6">
            <div className="flex items-center gap-6">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                <User className="w-10 h-10 text-white" />
              </div>
              <div className="flex-1">
                <CardTitle className="text-3xl text-blue-800 mb-2">{profile.first_name} {profile.last_name}</CardTitle>
                <div className="flex items-center gap-3">
                  <Badge
                    variant={profile.role === 'doctor' ? 'default' : 'secondary'}
                    className="text-sm px-3 py-1 bg-blue-100 text-blue-800 border-blue-300"
                  >
                    <UserCheck className="w-4 h-4 mr-2" />
                    {t(`auth.${profile.role}`)}
                  </Badge>
                  <div className="text-sm text-blue-600">
                    Member since {new Date(profile.created_at).getFullYear()}
                  </div>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                  <User className="w-4 h-4" />
                  First Name
                </label>
                <p className="text-lg font-semibold bg-muted/50 p-3 rounded-lg">
                  {profile.first_name}
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                  <User className="w-4 h-4" />
                  Last Name
                </label>
                <p className="text-lg font-semibold bg-muted/50 p-3 rounded-lg">
                  {profile.last_name}
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                <Mail className="w-4 h-4" />
                {t('profile.email')}
              </label>
              <p className="text-lg bg-muted/50 p-3 rounded-lg">{user.email}</p>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                <UserCheck className="w-4 h-4" />
                {t('profile.role')}
              </label>
              <div className="bg-muted/50 p-3 rounded-lg">
                <Badge variant={profile.role === 'doctor' ? 'default' : 'secondary'} className="text-sm">
                  {profile.role === 'doctor' ? 'Doctor' : 'Nurse'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Settings */}
        <Card className="shadow-soft">
          <CardHeader>
            <CardTitle>{t('profile.settings')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center text-muted-foreground">
              <p>Paramètres de profil disponibles ici</p>
            </div>


          </CardContent>
        </Card>
      </div>
    </div>
  );
}