import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useToast } from "@/hooks/use-toast";
import { useLanguage } from "@/contexts/LanguageContext";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { cn } from "@/lib/utils";

const patientSchema = z.object({
  firstName: z.string().min(1, "Veuillez saisir le prénom"),
  lastName: z.string().min(1, "Veuillez saisir le nom de famille"),
  birthDate: z.date().optional(),
  address: z.string().optional(),
  phoneNumber: z.string().optional(),
  medicalHistory: z.string().optional(),
  allergies: z.string().optional(),
  medicament: z.string().optional(),
  profession: z.string().optional(),
  codeCNSS: z.string().optional(),
  codeCNRPS: z.string().optional(),
});

type PatientFormData = z.infer<typeof patientSchema>;

interface AddPatientFormProps {
  onSuccess?: () => void;
  initialData?: any;
  isEditing?: boolean;
}

export function AddPatientForm({ onSuccess, initialData, isEditing = false }: AddPatientFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { t } = useLanguage();
  const { profile } = useAuth();
  const { toast } = useToast();

  const form = useForm<PatientFormData>({
    resolver: zodResolver(patientSchema),
    defaultValues: {
      firstName: initialData?.first_name || "",
      lastName: initialData?.last_name || "",
      birthDate: initialData?.birth_date ? new Date(initialData.birth_date) : undefined,
      address: initialData?.address || "",
      phoneNumber: initialData?.phone_number || "",
      medicalHistory: initialData?.medical_history || "",
      allergies: initialData?.allergies || "",
      medicament: initialData?.medicament || "",
      profession: initialData?.profession || "",
      codeCNSS: initialData?.code_cnss || "",
      codeCNRPS: initialData?.code_cnrps || "",
    },
  });

  const onSubmit = async (data: PatientFormData) => {
    setIsSubmitting(true);
    
    try {
      // Check if user has permission (only doctors can add patients)
      if (profile?.role !== 'doctor') {
        toast({
          title: t('common.error'),
          description: "Seuls les médecins peuvent ajouter des patients",
          variant: "destructive",
        });
        return;
      }

      // Insert or update patient in database
      const patientData = {
        first_name: data.firstName,
        last_name: data.lastName,
        birth_date: data.birthDate ? format(data.birthDate, 'yyyy-MM-dd') : null,
        address: data.address || null,
        phone_number: data.phoneNumber || null,
        medical_history: data.medicalHistory || null,
        allergies: data.allergies || null,
        medicament: data.medicament || null,
        profession: data.profession || null,
        code_cnss: data.codeCNSS || null,
        code_cnrps: data.codeCNRPS || null,
      };

      let error;
      if (isEditing && initialData) {
        const { error: updateError } = await supabase
          .from('patients')
          .update(patientData)
          .eq('id', initialData.id);
        error = updateError;
      } else {
        const { error: insertError } = await supabase
          .from('patients')
          .insert(patientData);
        error = insertError;
      }

      if (error) {
        console.error("Error adding patient:", error);
        toast({
          title: t('common.error'),
          description: error.message,
          variant: "destructive",
        });
        return;
      }
      
      // Show success toast
      toast({
        title: t('common.success'),
        description: isEditing ? 'Patient updated successfully' : t('message.patientAdded'),
      });
      
      // Reset form
      form.reset();
      
      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error adding patient:", error);
      toast({
        title: t('common.error'),
        description: "Erreur lors de l'ajout du patient",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Don't show form if user is not a doctor
  if (profile?.role !== 'doctor') {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">
          Seuls les médecins peuvent ajouter des patients.
        </p>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="lastName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('patient.lastName')} *</FormLabel>
                <FormControl>
                  <Input
                    placeholder={t('patient.lastName')}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="firstName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('patient.firstName')} *</FormLabel>
                <FormControl>
                  <Input
                    placeholder={t('patient.firstName')}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="birthDate"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>{t('patient.birthDate')}</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>{t('patient.birthDate')}</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) =>
                        date > new Date() || date < new Date("1900-01-01")
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phoneNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('patient.phone')}</FormLabel>
                <FormControl>
                  <Input
                    placeholder={t('patient.phone')}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('form.address')}</FormLabel>
              <FormControl>
                <Input
                  placeholder={t('form.address')}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="profession"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('form.profession')}</FormLabel>
              <FormControl>
                <Input
                  placeholder={t('form.profession')}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="medicament"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('form.medicament')}</FormLabel>
              <FormControl>
                <Textarea
                  placeholder={t('form.medicament')}
                  rows={3}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="codeCNSS"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('form.codeCNSS')}</FormLabel>
                <FormControl>
                  <Input
                    placeholder={t('form.codeCNSS')}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="codeCNRPS"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('form.codeCNRPS')}</FormLabel>
                <FormControl>
                  <Input
                    placeholder={t('form.codeCNRPS')}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="medicalHistory"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('form.medicalHistory')}</FormLabel>
              <FormControl>
                <Textarea
                  placeholder={t('form.medicalHistory')}
                  rows={4}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="allergies"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('form.allergies')}</FormLabel>
              <FormControl>
                <Textarea
                  placeholder={t('form.allergies')}
                  rows={3}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex gap-4 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => form.reset()}
            disabled={isSubmitting}
          >
            {t('common.reset')}
          </Button>
          
          <Button 
            type="submit" 
            disabled={isSubmitting}
            className="flex-1"
          >
            {isSubmitting ? t('common.loading') : t('patient.add')}
          </Button>
        </div>
      </form>
    </Form>
  );
}