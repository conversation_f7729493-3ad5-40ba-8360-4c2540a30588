# 📧 Supabase Email Configuration for ClinicLine

## 🎯 **CONFIGURATION STEPS**

### **STEP 1: Site URL Configuration**

1. **Go to**: Supabase Dashboard → Authentication → Settings → General
2. **Set Site URL**:
   - **Development**: `http://localhost:8080`
   - **Production**: `https://yourdomain.com`

### **STEP 2: Redirect URLs Configuration**

1. **Go to**: Authentication → Settings → General → Redirect URLs
2. **Add these URLs**:
   ```
   http://localhost:8080/reset-password
   https://yourdomain.com/reset-password
   ```

### **STEP 3: Email Templates Configuration**

#### **Reset Password Email Template:**

1. **Go to**: Authentication → Settings → Email Templates
2. **Select**: "Reset Password" template
3. **Replace with this French template**:

```html
<h2>Réinitialisation de votre mot de passe ClinicLine</h2>

<p>Bon<PERSON><PERSON>,</p>

<p>Vous avez demandé la réinitialisation de votre mot de passe pour votre compte ClinicLine.</p>

<p>Cliquez sur le lien ci-dessous pour créer un nouveau mot de passe :</p>

<p><a href="{{ .SiteURL }}/reset-password?access_token={{ .TokenHash }}&refresh_token={{ .RefreshTokenHash }}&type=recovery">Réinitialiser mon mot de passe</a></p>

<p>Ce lien expirera dans 1 heure pour des raisons de sécurité.</p>

<p>Si vous n'avez pas demandé cette réinitialisation, vous pouvez ignorer cet email en toute sécurité.</p>

<p>Cordialement,<br>
L'équipe ClinicLine</p>

<hr>
<p style="font-size: 12px; color: #666;">
Si le lien ne fonctionne pas, copiez et collez cette URL dans votre navigateur :<br>
{{ .SiteURL }}/reset-password?access_token={{ .TokenHash }}&refresh_token={{ .RefreshTokenHash }}&type=recovery
</p>
```

#### **Email Subject:**
```
Réinitialisation de votre mot de passe ClinicLine
```

### **STEP 4: SMTP Configuration (Optional - Recommended for Production)**

#### **For Better Email Delivery:**

1. **Go to**: Authentication → Settings → SMTP Settings
2. **Enable Custom SMTP**
3. **Configure with your email provider**:

#### **Gmail SMTP Example:**
```
SMTP Host: smtp.gmail.com
SMTP Port: 587
SMTP User: <EMAIL>
SMTP Pass: your-app-password
Sender Name: ClinicLine
Sender Email: <EMAIL>
```

#### **Other Popular SMTP Providers:**

**SendGrid:**
```
SMTP Host: smtp.sendgrid.net
SMTP Port: 587
SMTP User: apikey
SMTP Pass: your-sendgrid-api-key
```

**Mailgun:**
```
SMTP Host: smtp.mailgun.org
SMTP Port: 587
SMTP User: your-mailgun-username
SMTP Pass: your-mailgun-password
```

### **STEP 5: Email Rate Limiting**

1. **Go to**: Authentication → Settings → Rate Limits
2. **Configure**:
   - **Email sending rate**: 30 emails per hour per IP
   - **Password reset rate**: 5 attempts per hour per email

### **STEP 6: Security Settings**

1. **Go to**: Authentication → Settings → Security
2. **Configure**:
   - **JWT expiry**: 3600 seconds (1 hour)
   - **Refresh token rotation**: Enabled
   - **Double confirm email changes**: Enabled

## 🧪 **TESTING THE EMAIL CONFIGURATION**

### **Test in Development:**

1. **Start your app**: `npm run dev`
2. **Go to**: `http://localhost:8080/auth`
3. **Click**: "Mot de passe oublié ?"
4. **Enter**: A valid email address
5. **Check**: Email inbox for reset email
6. **Verify**: Link redirects to reset password page

### **Test Email Template Variables:**

The email template uses these Supabase variables:
- `{{ .SiteURL }}` - Your configured site URL
- `{{ .TokenHash }}` - Secure access token
- `{{ .RefreshTokenHash }}` - Refresh token
- `{{ .Email }}` - User's email address

## 🚀 **PRODUCTION DEPLOYMENT CHECKLIST**

### **Before Going Live:**

- [ ] **Update Site URL** to production domain
- [ ] **Add production redirect URLs**
- [ ] **Configure custom SMTP** (recommended)
- [ ] **Test email delivery** from production
- [ ] **Verify SSL certificates** for secure links
- [ ] **Set up email monitoring** (optional)

### **DNS Configuration (if using custom domain):**

If using custom SMTP with your domain:
```
SPF Record: v=spf1 include:_spf.google.com ~all
DKIM: Configure with your email provider
DMARC: v=DMARC1; p=quarantine; rua=mailto:<EMAIL>
```

## 🔧 **TROUBLESHOOTING**

### **Common Issues:**

#### **Email Not Received:**
1. Check spam/junk folder
2. Verify email address is correct
3. Check SMTP configuration
4. Verify rate limits not exceeded

#### **Reset Link Not Working:**
1. Check redirect URLs configuration
2. Verify Site URL is correct
3. Ensure link hasn't expired (1 hour default)
4. Check browser console for errors

#### **SMTP Authentication Failed:**
1. Verify SMTP credentials
2. Check if 2FA is enabled (use app password)
3. Verify SMTP host and port
4. Check firewall/network restrictions

### **Debug Mode:**

Enable debug logging in Supabase:
1. Go to Logs → Auth Logs
2. Monitor email sending attempts
3. Check for error messages

## 📊 **MONITORING EMAIL DELIVERY**

### **Supabase Dashboard:**
- **Auth Logs**: Monitor email sending
- **Usage Stats**: Track email volume
- **Error Logs**: Debug delivery issues

### **Email Provider Analytics:**
- **Delivery rates**: Monitor successful sends
- **Bounce rates**: Track failed deliveries
- **Open rates**: See user engagement

## 🎨 **CUSTOMIZATION OPTIONS**

### **Advanced Email Template:**

You can customize the email template with:
- **Company branding/logo**
- **Custom CSS styling**
- **Multiple languages**
- **Dynamic content**

### **Example with Logo:**
```html
<div style="text-align: center; margin-bottom: 30px;">
  <img src="https://yourdomain.com/logo.png" alt="ClinicLine" style="max-width: 200px;">
</div>

<h2 style="color: #6366f1;">Réinitialisation de votre mot de passe</h2>
<!-- Rest of template -->
```

## ✅ **CONFIGURATION COMPLETE**

Once configured, your reset password feature will:
- ✅ Send professional French emails
- ✅ Use secure token-based authentication
- ✅ Provide excellent user experience
- ✅ Handle errors gracefully
- ✅ Work reliably in production

The email system is now ready for production use! 🚀
