import { useEffect, useState } from "react";
import { supabase } from "../integrations/supabase/client";
import { Tables } from "../integrations/supabase/types";
import { useAuth } from "../contexts/AuthContext";

export default function AdminDashboard() {
  const { user } = useAuth();
  const [profiles, setProfiles] = useState<Tables<"profiles">[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchProfiles() {
      setLoading(true);
      const { data, error } = await supabase.from("profiles").select("*");
      if (!error && data) setProfiles(data);
      setLoading(false);
    }
    fetchProfiles();
  }, []);

  // Only show for admin
  if (!user || user.role !== "admin") return null;

  const handleUpdate = async (id: string, changes: Partial<Tables<"profiles">>) => {
    await supabase.from("profiles").update(changes).eq("id", id);
    setProfiles((prev) =>
      prev.map((p) => (p.id === id ? { ...p, ...changes } : p))
    );
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">User Payments Management</h1>
      {loading ? (
        <div>Loading...</div>
      ) : (
        <table className="min-w-full border">
          <thead>
            <tr>
              <th>Name</th>
              <th>Role</th>
              <th>Payed</th>
              <th>Blocked</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {profiles.map((profile) => (
              <tr key={profile.id}>
                <td>{profile.first_name} {profile.last_name}</td>
                <td>
                  <select
                    value={profile.role}
                    onChange={e => handleUpdate(profile.id, { role: e.target.value })}
                  >
                    <option value="user">User</option>
                    <option value="admin">Admin</option>
                  </select>
                </td>
                <td>
                  <input
                    type="checkbox"
                    checked={profile.payed}
                    onChange={e => handleUpdate(profile.id, { payed: e.target.checked })}
                  />
                </td>
                <td>
                  <input
                    type="radio"
                    name={`blocked-${profile.id}`}
                    checked={profile.blocked}
                    onChange={() => handleUpdate(profile.id, { blocked: true })}
                  /> Blocked
                  <input
                    type="radio"
                    name={`blocked-${profile.id}`}
                    checked={!profile.blocked}
                    onChange={() => handleUpdate(profile.id, { blocked: false })}
                  /> Allowed
                </td>
                <td>
                  {/* Additional actions if needed */}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
}
