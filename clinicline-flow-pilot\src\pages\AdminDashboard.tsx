import { useState, useEffect } from "react";
import { <PERSON>, User<PERSON><PERSON><PERSON>, UserX, Shield, CreditCard, Eye } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { useToast } from "@/hooks/use-toast";
import { format } from "date-fns";
import { fr } from "date-fns/locale";

interface User {
  id: string;
  first_name: string;
  last_name: string;
  role: 'doctor' | 'nurse' | 'admin';
  created_at: string;
  active: boolean;
  payed: boolean;
}

export default function AdminDashboard() {
  const { profile } = useAuth();
  const { t } = useLanguage();
  const { toast } = useToast();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userDialogOpen, setUserDialogOpen] = useState(false);
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    blockedUsers: 0,
    paidUsers: 0
  });

  // Check if user is admin
  if (profile?.role !== 'admin') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-96">
          <CardHeader>
            <CardTitle className="text-center text-red-600">Accès Refusé</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-center text-muted-foreground">
              Vous n'avez pas les permissions nécessaires pour accéder à cette page.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching users:', error);
        toast({
          title: "Erreur",
          description: "Impossible de charger les utilisateurs",
          variant: "destructive",
        });
        return;
      }

      setUsers(data || []);
      
      // Calculate stats
      const totalUsers = data?.length || 0;
      const activeUsers = data?.filter(user => user.active).length || 0;
      const blockedUsers = totalUsers - activeUsers;
      const paidUsers = data?.filter(user => user.payed).length || 0;
      
      setStats({
        totalUsers,
        activeUsers,
        blockedUsers,
        paidUsers
      });
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateUserStatus = async (userId: string, active: boolean) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ active })
        .eq('id', userId);

      if (error) {
        console.error('Error updating user status:', error);
        toast({
          title: "Erreur",
          description: "Impossible de mettre à jour le statut de l'utilisateur",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Succès",
        description: `Utilisateur ${active ? 'activé' : 'bloqué'} avec succès`,
      });

      fetchUsers(); // Refresh the list
    } catch (error) {
      console.error('Error updating user status:', error);
    }
  };

  const updatePaymentStatus = async (userId: string, payed: boolean) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ payed })
        .eq('id', userId);

      if (error) {
        console.error('Error updating payment status:', error);
        toast({
          title: "Erreur",
          description: "Impossible de mettre à jour le statut de paiement",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Succès",
        description: `Statut de paiement mis à jour avec succès`,
      });

      fetchUsers(); // Refresh the list
    } catch (error) {
      console.error('Error updating payment status:', error);
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'doctor':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'nurse':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return 'Administrateur';
      case 'doctor':
        return 'Médecin';
      case 'nurse':
        return 'Infirmière';
      default:
        return role;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-red-600 via-purple-600 to-indigo-600 rounded-2xl p-6 text-white shadow-xl">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold mb-2">Tableau de Bord Administrateur</h1>
            <p className="text-red-100 text-lg">
              Gérez les utilisateurs et contrôlez les accès - {format(new Date(), 'EEEE dd MMMM yyyy', { locale: fr })}
            </p>
          </div>
          <Shield className="w-16 h-16 text-red-200" />
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="shadow-soft">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Utilisateurs</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              Tous les utilisateurs enregistrés
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-soft">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Utilisateurs Actifs</CardTitle>
            <UserCheck className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.activeUsers}</div>
            <p className="text-xs text-muted-foreground">
              Comptes actifs et accessibles
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-soft">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Utilisateurs Bloqués</CardTitle>
            <UserX className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.blockedUsers}</div>
            <p className="text-xs text-muted-foreground">
              Comptes bloqués ou suspendus
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-soft">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Utilisateurs Payés</CardTitle>
            <CreditCard className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.paidUsers}</div>
            <p className="text-xs text-muted-foreground">
              Comptes avec paiement validé
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Users Management Table */}
      <Card className="shadow-soft">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Gestion des Utilisateurs
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Utilisateur</TableHead>
                  <TableHead>Rôle</TableHead>
                  <TableHead>Date d'inscription</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead>Paiement</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{user.first_name} {user.last_name}</div>
                        <div className="text-sm text-muted-foreground">{user.id}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getRoleBadgeColor(user.role)}>
                        {getRoleLabel(user.role)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {format(new Date(user.created_at), 'dd MMM yyyy', { locale: fr })}
                    </TableCell>
                    <TableCell>
                      <RadioGroup
                        value={user.active ? "active" : "blocked"}
                        onValueChange={(value) => updateUserStatus(user.id, value === "active")}
                        className="flex flex-row space-x-4"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="active" id={`active-${user.id}`} />
                          <Label htmlFor={`active-${user.id}`} className="text-green-600">Actif</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="blocked" id={`blocked-${user.id}`} />
                          <Label htmlFor={`blocked-${user.id}`} className="text-red-600">Bloqué</Label>
                        </div>
                      </RadioGroup>
                    </TableCell>
                    <TableCell>
                      <RadioGroup
                        value={user.payed ? "paid" : "unpaid"}
                        onValueChange={(value) => updatePaymentStatus(user.id, value === "paid")}
                        className="flex flex-row space-x-4"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="paid" id={`paid-${user.id}`} />
                          <Label htmlFor={`paid-${user.id}`} className="text-blue-600">Payé</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="unpaid" id={`unpaid-${user.id}`} />
                          <Label htmlFor={`unpaid-${user.id}`} className="text-orange-600">Non payé</Label>
                        </div>
                      </RadioGroup>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedUser(user);
                          setUserDialogOpen(true);
                        }}
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        Voir
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* User Details Dialog */}
      <Dialog open={userDialogOpen} onOpenChange={setUserDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Détails de l'Utilisateur</DialogTitle>
            <DialogDescription>
              Informations complètes sur l'utilisateur sélectionné
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Prénom</Label>
                  <p className="text-lg">{selectedUser.first_name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Nom</Label>
                  <p className="text-lg">{selectedUser.last_name}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Rôle</Label>
                  <Badge className={getRoleBadgeColor(selectedUser.role)}>
                    {getRoleLabel(selectedUser.role)}
                  </Badge>
                </div>
                <div>
                  <Label className="text-sm font-medium">ID Utilisateur</Label>
                  <p className="text-sm font-mono bg-gray-100 p-2 rounded">{selectedUser.id}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Date d'inscription</Label>
                  <p>{format(new Date(selectedUser.created_at), 'dd MMMM yyyy à HH:mm', { locale: fr })}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Statut du compte</Label>
                  <Badge className={selectedUser.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                    {selectedUser.active ? 'Actif' : 'Bloqué'}
                  </Badge>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium">Statut de paiement</Label>
                <Badge className={selectedUser.payed ? 'bg-blue-100 text-blue-800' : 'bg-orange-100 text-orange-800'}>
                  {selectedUser.payed ? 'Payé' : 'Non payé'}
                </Badge>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setUserDialogOpen(false)}>
              Fermer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
