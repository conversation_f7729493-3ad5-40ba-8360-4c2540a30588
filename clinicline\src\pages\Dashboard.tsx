import { useState, useEffect } from "react";
import { Calendar, Users, Clock, TrendingUp, TrendingDown, Minus, MoreVertical, Plus } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { format, isToday, startOfWeek, endOfWeek, subDays, subWeeks } from "date-fns";
import { fr } from "date-fns/locale";
import { useToast } from "@/hooks/use-toast";
import { AddAppointmentForm } from "@/components/forms/AddAppointmentForm";

interface DashboardStats {
  totalPatients: number;
  todayAppointments: number;
  upcomingAppointments: number;
  weeklyAppointments: number;
  patientsGrowth: number;
  appointmentsGrowth: number;
  upcomingGrowth: number;
  weeklyGrowth: number;
}

interface TodayAppointment {
  id: number;
  appointment_time: string;
  patient_id: number;
  status: "confirmed" | "completed" | "cancelled";
  notes?: string;
  patients: {
    first_name: string;
    last_name: string;
  };
}

export default function Dashboard() {
  const { profile } = useAuth();
  const { t } = useLanguage();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [stats, setStats] = useState<DashboardStats>({
    totalPatients: 0,
    todayAppointments: 0,
    upcomingAppointments: 0,
    weeklyAppointments: 0,
    patientsGrowth: 0,
    appointmentsGrowth: 0,
    upcomingGrowth: 0,
    weeklyGrowth: 0,
  });
  const [todayAppointments, setTodayAppointments] = useState<TodayAppointment[]>([]);
  const [patients, setPatients] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [appointmentDialogOpen, setAppointmentDialogOpen] = useState(false);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const calculateGrowthMetrics = async (
    currentPatients: number,
    currentTodayAppointments: number,
    currentUpcomingAppointments: number,
    currentWeeklyAppointments: number
  ) => {
    try {
      const today = new Date();
      const yesterday = subDays(today, 1);
      const lastWeek = subWeeks(today, 1);
      const lastWeekStart = format(startOfWeek(lastWeek), 'yyyy-MM-dd');
      const lastWeekEnd = format(endOfWeek(lastWeek), 'yyyy-MM-dd');

      // Get yesterday's appointments count
      const { count: yesterdayAppointments } = await supabase
        .from('appointments')
        .select('*', { count: 'exact', head: true })
        .eq('appointment_date', format(yesterday, 'yyyy-MM-dd'));

      // Get last week's appointments count
      const { count: lastWeekAppointments } = await supabase
        .from('appointments')
        .select('*', { count: 'exact', head: true })
        .gte('appointment_date', lastWeekStart)
        .lte('appointment_date', lastWeekEnd);

      // Get patients count from a week ago (approximate growth)
      const oneWeekAgo = format(subWeeks(today, 1), 'yyyy-MM-dd');
      const { count: patientsWeekAgo } = await supabase
        .from('patients')
        .select('*', { count: 'exact', head: true })
        .lte('created_at', oneWeekAgo);

      // Get upcoming appointments from last week for comparison
      const lastWeekUpcomingStart = format(subDays(lastWeek, 7), 'yyyy-MM-dd');
      const lastWeekUpcomingEnd = format(lastWeek, 'yyyy-MM-dd');
      const { count: lastWeekUpcoming } = await supabase
        .from('appointments')
        .select('*', { count: 'exact', head: true })
        .gt('appointment_date', lastWeekUpcomingStart)
        .lte('appointment_date', lastWeekUpcomingEnd);

      // Calculate percentage growth
      const calculatePercentageGrowth = (current: number, previous: number) => {
        if (previous === 0) return current > 0 ? 100 : 0;
        return Math.round(((current - previous) / previous) * 100);
      };

      return {
        patientsGrowth: calculatePercentageGrowth(currentPatients, patientsWeekAgo || 0),
        appointmentsGrowth: calculatePercentageGrowth(currentTodayAppointments, yesterdayAppointments || 0),
        upcomingGrowth: calculatePercentageGrowth(currentUpcomingAppointments, lastWeekUpcoming || 0),
        weeklyGrowth: calculatePercentageGrowth(currentWeeklyAppointments, lastWeekAppointments || 0),
      };
    } catch (error) {
      console.error('Error calculating growth metrics:', error);
      // Return default values if calculation fails
      return {
        patientsGrowth: 0,
        appointmentsGrowth: 0,
        upcomingGrowth: 0,
        weeklyGrowth: 0,
      };
    }
  };

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Fetch total patients
      const { count: totalPatients } = await supabase
        .from('patients')
        .select('*', { count: 'exact', head: true });

      // Fetch today's appointments
      const today = format(new Date(), 'yyyy-MM-dd');
      const { data: todayAppts, error: todayError } = await supabase
        .from('appointments')
        .select(`
          id,
          appointment_time,
          patient_id,
          status,
          notes,
          patients!inner (
            first_name,
            last_name
          )
        `)
        .eq('appointment_date', today)
        .order('appointment_time');

      if (todayError) {
        console.error('Error fetching today appointments:', todayError);
      } else {
        setTodayAppointments(todayAppts || []);
      }

      // Fetch upcoming appointments (next 7 days, excluding today)
      const nextWeek = format(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd');
      const { count: upcomingCount } = await supabase
        .from('appointments')
        .select('*', { count: 'exact', head: true })
        .gt('appointment_date', today)
        .lte('appointment_date', nextWeek);

      // Fetch this week's appointments
      const weekStart = format(startOfWeek(new Date()), 'yyyy-MM-dd');
      const weekEnd = format(endOfWeek(new Date()), 'yyyy-MM-dd');
      const { count: weeklyCount } = await supabase
        .from('appointments')
        .select('*', { count: 'exact', head: true })
        .gte('appointment_date', weekStart)
        .lte('appointment_date', weekEnd);

      // Fetch patients for today's summary
      const { data: patientsData, error: patientsError } = await supabase
        .from('patients')
        .select('*');

      if (patientsError) {
        console.error('Error fetching patients:', patientsError);
      } else {
        setPatients(patientsData || []);
      }

      // Calculate real growth indicators by comparing with previous periods
      const growthData = await calculateGrowthMetrics(
        totalPatients || 0,
        todayAppts?.length || 0,
        upcomingCount || 0,
        weeklyCount || 0
      );

      setStats({
        totalPatients: totalPatients || 0,
        todayAppointments: todayAppts?.length || 0,
        upcomingAppointments: upcomingCount || 0,
        weeklyAppointments: weeklyCount || 0,
        patientsGrowth: growthData.patientsGrowth,
        appointmentsGrowth: growthData.appointmentsGrowth,
        upcomingGrowth: growthData.upcomingGrowth,
        weeklyGrowth: growthData.weeklyGrowth,
      });

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateAppointmentStatus = async (appointmentId: number, newStatus: string) => {
    try {
      const { data, error } = await supabase
        .from('appointments')
        .update({ status: newStatus })
        .eq('id', appointmentId)
        .select();

      if (error) {
        console.error('Error updating appointment status:', error);
        toast({
          title: "Error",
          description: `Failed to update appointment status: ${error.message}`,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Success",
          description: `Appointment marked as ${newStatus}`,
        });
        fetchDashboardData(); // Refresh the dashboard data
      }
    } catch (error) {
      console.error('Error updating appointment status:', error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "confirmed":
        return "default";
      case "completed":
        return "secondary";
      case "cancelled":
        return "destructive";
      default:
        return "outline";
    }
  };

  const getGrowthIndicator = (growth: number) => {
    if (growth > 0) {
      return {
        icon: TrendingUp,
        color: "text-green-600",
        bgColor: "bg-green-50",
        text: `+${growth}%`,
        label: "increase"
      };
    } else if (growth < 0) {
      return {
        icon: TrendingDown,
        color: "text-red-600",
        bgColor: "bg-red-50",
        text: `${growth}%`,
        label: "decrease"
      };
    } else {
      return {
        icon: Minus,
        color: "text-gray-600",
        bgColor: "bg-gray-50",
        text: "0%",
        label: "no change"
      };
    }
  };

  const dashboardStats = [
    {
      title: t('dashboard.totalPatients'),
      value: loading ? t('common.loading') : stats.totalPatients.toString(),
      icon: Users,
      color: "text-primary",
      growth: stats.patientsGrowth,
    },
    {
      title: t('dashboard.todayAppointments'),
      value: loading ? t('common.loading') : stats.todayAppointments.toString(),
      icon: Calendar,
      color: "text-success",
      growth: stats.appointmentsGrowth,
    },
    {
      title: t('dashboard.upcomingAppointments'),
      value: loading ? t('common.loading') : stats.upcomingAppointments.toString(),
      icon: Clock,
      color: "text-warning",
      growth: stats.upcomingGrowth,
    },
    {
      title: t('dashboard.thisWeek'),
      value: loading ? t('common.loading') : stats.weeklyAppointments.toString(),
      icon: TrendingUp,
      color: "text-primary",
      growth: stats.weeklyGrowth,
    },
  ];

  return (
    <div className="space-y-6 relative">
      {/* Enhanced Header */}
      <div className="bg-gradient-to-r from-emerald-600 via-teal-600 to-cyan-600 rounded-2xl p-6 text-white shadow-xl">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold mb-2">{t('dashboard.title')}</h1>
            <p className="text-emerald-100 text-lg">
              {t('dashboard.welcome')}, {profile ? `${profile.first_name} ${profile.last_name}` : t('ui.doctor')}! {t('dashboard.overview')} {format(new Date(), 'EEEE dd MMMM yyyy', { locale: fr })}
            </p>
            <div className="flex items-center gap-4 mt-3">
              <div className="flex items-center gap-2 bg-white/20 rounded-full px-3 py-1">
                <div className="w-2 h-2 rounded-full bg-green-400"></div>
                <span className="text-sm">{t('common.today')}: {todayAppointments.length} {t('dashboard.appointments')}</span>
              </div>
              <div className="flex items-center gap-2 bg-white/20 rounded-full px-3 py-1">
                <div className="w-2 h-2 rounded-full bg-blue-400"></div>
                <span className="text-sm">{t('dashboard.patients')}: {patients.length} {t('dashboard.total')}</span>
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-3">
            <Button
              className="bg-white text-emerald-600 hover:bg-emerald-50 shadow-lg font-semibold px-6 py-3 text-lg"
              onClick={() => setAppointmentDialogOpen(true)}
            >
              <Plus className="w-5 h-5 mr-2" />
              {t('dashboard.newAppointment')}
            </Button>
            <div className="text-center text-emerald-100 text-sm">
              {t('dashboard.quickAppointmentBooking')}
            </div>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {dashboardStats.map((stat) => {
          const growthIndicator = getGrowthIndicator(stat.growth);
          return (
            <Card key={stat.title} className="shadow-soft hover:shadow-medium transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {stat.title}
                </CardTitle>
                <stat.icon className={`h-5 w-5 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <div className="flex items-center gap-2 mt-2">
                  <div className={`flex items-center gap-1 px-2 py-1 rounded-full ${growthIndicator.bgColor}`}>
                    <growthIndicator.icon className={`h-3 w-3 ${growthIndicator.color}`} />
                    <span className={`text-xs font-medium ${growthIndicator.color}`}>
                      {growthIndicator.text}
                    </span>
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {t('dashboard.vsLastPeriod')}
                  </span>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Today's Appointments */}
        <Card className="shadow-soft">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-primary" />
              {t('dashboard.todayAppointments')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {loading ? (
                <div className="text-center py-4">
                  <p className="text-muted-foreground">{t('common.loading')}</p>
                </div>
              ) : todayAppointments.length === 0 ? (
                <div className="text-center py-4">
                  <p className="text-muted-foreground">Aucun rendez-vous programmé aujourd'hui</p>
                </div>
              ) : (
                todayAppointments.map((appointment) => (
                  <div
                    key={appointment.id}
                    className="flex items-center justify-between p-3 bg-muted/50 rounded-lg hover:bg-muted transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div className="text-sm font-medium text-primary">
                        {appointment.appointment_time}
                      </div>
                      <div>
                        <p className="font-medium">
                          {appointment.patients.first_name} {appointment.patients.last_name}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {appointment.notes || 'General consultation'}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={getStatusColor(appointment.status)}>
                        {appointment.status}
                      </Badge>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          <DropdownMenuItem
                            onClick={() => updateAppointmentStatus(appointment.id, 'confirmed')}
                            disabled={appointment.status === 'confirmed'}
                            className="flex items-center gap-2"
                          >
                            <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                            Mark as Confirmed
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => updateAppointmentStatus(appointment.id, 'completed')}
                            disabled={appointment.status === 'completed'}
                            className="flex items-center gap-2"
                          >
                            <div className="w-2 h-2 rounded-full bg-green-500"></div>
                            Mark as Completed
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => updateAppointmentStatus(appointment.id, 'cancelled')}
                            disabled={appointment.status === 'cancelled'}
                            className="text-destructive flex items-center gap-2"
                          >
                            <div className="w-2 h-2 rounded-full bg-red-500"></div>
                            Cancel Appointment
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))
              )}
            </div>
            <Button
              variant="outline"
              className="w-full mt-4"
              onClick={() => navigate('/appointments')}
            >
              Voir Tous les Rendez-vous
            </Button>
          </CardContent>
        </Card>

        {/* Today's Summary */}
        <Card className="shadow-soft">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-primary" />
              {t('dashboard.todaySummary')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid gap-4">
                <div className="flex justify-between text-sm">
                  <span>{t('dashboard.totalPatients')}</span>
                  <span className="font-medium text-primary">
                    {patients.length}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>{t('dashboard.todayAppointments')}</span>
                  <span className="font-medium text-blue-600">
                    {todayAppointments.length}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Add Appointment Dialog */}
      <Dialog open={appointmentDialogOpen} onOpenChange={setAppointmentDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Schedule New Appointment</DialogTitle>
          </DialogHeader>
          <AddAppointmentForm
            onSuccess={() => {
              setAppointmentDialogOpen(false);
              fetchDashboardData(); // Refresh dashboard data
            }}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}