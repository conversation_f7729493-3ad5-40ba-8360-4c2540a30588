-- Re-enable RLS on tables (in case it was disabled for testing)
ALTER TABLE public.patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.appointments ENABLE ROW LEVEL SECURITY;

-- Drop existing restrictive policies for patients
DROP POLICY IF EXISTS "Doctors can manage all patients" ON public.patients;
DROP POLICY IF EXISTS "Nurses can view all patients" ON public.patients;

-- Drop existing restrictive policies for appointments
DROP POLICY IF EXISTS "Doctors can manage all appointments" ON public.appointments;
DROP POLICY IF EXISTS "Nurses can view all appointments" ON public.appointments;

-- Create new policies allowing both doctors and nurses full CRUD access to patients
CREATE POLICY "Healthcare staff can manage all patients" 
ON public.patients 
FOR ALL 
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role IN ('doctor', 'nurse')
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role IN ('doctor', 'nurse')
  )
);

-- Create new policies allowing both doctors and nurses full CRUD access to appointments
CREATE POLICY "Healthcare staff can manage all appointments" 
ON public.appointments 
FOR ALL 
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role IN ('doctor', 'nurse')
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role IN ('doctor', 'nurse')
  )
);